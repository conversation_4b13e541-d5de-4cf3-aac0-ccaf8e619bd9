"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CatGallery } from "@/components/cat-gallery";
import { ChatButton } from "@/components/chat-button";
import { CatStatusMenu } from "@/components/cat-status-menu";
import {
	Heart,
	Share2,
	MapPin,
	CheckCircle2,
	Edit,
	Loader2,
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { CatDetail } from "@/lib/types/cat";
import { useSession } from "@/lib/auth/client";
import { useFavorites } from "@/hooks/use-favorites";

interface CatDetailsClientProps {
	cat: CatDetail;
	lang: string;
	slug: string;
}

export function CatDetailsClient({ cat, lang, slug }: CatDetailsClientProps) {
	const t = useTranslations("cats");
	const common = useTranslations("common");
	const utils = api.useUtils();

	// Get current user session
	const { data: session } = useSession();

	// Use favorites hook with additional queries to invalidate
	const { isFavorite, isPending, handleToggleFavorite } = useFavorites({
		additionalQueriesToInvalidate: [
			() => utils.cats.getBySlug.invalidate(),
		],
	});

	// Check if this cat is favorited and pending
	const isCatFavorite = isFavorite(cat.id);
	const isFavoritePending = isPending(cat.id);

	// Check if user is authorized to edit
	const canEdit = session?.user && session.user.id === cat.userId?.toString();

	// Determine text direction based on locale
	const isRTL = lang === "ar";
	const textDirection = isRTL ? "rtl" : "ltr";

	return (
		<main className="min-h-screen bg-white" dir={textDirection}>
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					{/* Left Column - Images */}
					<div className="lg:col-span-2">
						{/* Main Image */}
						<div className="relative mb-6">
							<CatGallery
								images={
									cat.images?.map((img) => img.url) || [
										cat.imageUrl || "/cat.jpeg",
									]
								}
								name={cat.name}
							/>
						</div>

						{/* Health Status Card */}
						<div className="bg-white border border-gray-200 rounded-2xl p-6">
							<div className="space-y-4">
								<h3 className="font-semibold text-lg text-gray-900">
									{t("healthStatus")}
								</h3>
								<div className="space-y-3">
									{/* Spayed/Neutered Status */}
									<div className="flex items-center gap-3">
										<div
											className={cn(
												"flex items-center justify-center w-6 h-6 rounded-full",
												cat.neutered
													? "bg-green-100"
													: "bg-gray-100"
											)}
										>
											<CheckCircle2
												className={cn(
													"h-4 w-4",
													cat.neutered
														? "text-green-600"
														: "text-gray-400"
												)}
											/>
										</div>
										<span className="text-sm font-medium text-gray-900 flex-1">
											{t("spayedNeutered")}
										</span>
										<span
											className={cn(
												"text-xs px-2 py-1 rounded-full",
												cat.neutered
													? "bg-green-50 text-green-700"
													: "bg-gray-50 text-gray-600"
											)}
										></span>
									</div>

									{/* Vaccination Status */}
									<div className="flex items-center gap-3">
										<div
											className={cn(
												"flex items-center justify-center w-6 h-6 rounded-full",
												cat.vaccinated
													? "bg-green-100"
													: "bg-gray-100"
											)}
										>
											<CheckCircle2
												className={cn(
													"h-4 w-4",
													cat.vaccinated
														? "text-green-600"
														: "text-gray-400"
												)}
											/>
										</div>
										<span className="text-sm font-medium text-gray-900 flex-1">
											{t("filters.vaccinated")}
										</span>
										<span
											className={cn(
												"text-xs px-2 py-1 rounded-full",
												cat.vaccinated
													? "bg-green-50 text-green-700"
													: "bg-gray-50 text-gray-600"
											)}
										></span>
									</div>

									{/* Special Needs Status */}
									{cat.specialNeeds && (
										<div className="flex items-center gap-3">
											<div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100">
												<CheckCircle2 className="h-4 w-4 text-blue-600" />
											</div>
											<span className="text-sm font-medium text-gray-900 flex-1">
												{t("specialNeeds")}
											</span>
											<span className="text-xs px-2 py-1 rounded-full bg-blue-50 text-blue-700">
												{t("specialNeeds")}
											</span>
										</div>
									)}

									{/* Special Needs Description */}
									{cat.specialNeeds &&
										cat.specialNeedsDescription && (
											<div className="mt-3 p-3 bg-blue-50 rounded-lg">
												<p className="text-sm text-blue-800">
													{
														cat.specialNeedsDescription
													}
												</p>
											</div>
										)}
								</div>
							</div>
						</div>

						{/* Cat Details Card */}
						<div className="bg-white border border-gray-200 rounded-2xl p-6 mt-7">
							<h2 className="text-2xl font-display font-bold text-gray-900 mb-4">
								{t("details.about")} {cat.name}
							</h2>
							<p className="text-gray-700 text-lg leading-relaxed whitespace-pre-line">
								{cat.description}
							</p>
						</div>

						{/* Story Section - if available */}
						{cat.story && (
							<div className="bg-white border border-gray-200 rounded-2xl p-6 mt-7">
								<h2 className="text-2xl font-display font-bold text-gray-900 mb-4">
									{t("details.story")}
								</h2>
								<p className="text-gray-700 text-lg leading-relaxed whitespace-pre-line">
									{cat.story}
								</p>
							</div>
						)}
					</div>

					{/* Right Column - Info & Actions */}
					<div className="space-y-6">
						{/* Main Info Card */}
						<div className="bg-white border border-gray-200 rounded-2xl p-6 sticky top-24">
							<div className="flex items-start justify-between mb-4">
								<div>
									<h1 className="text-3xl font-display font-bold text-gray-900 mb-2">
										{cat.name}
									</h1>
									<div className="flex items-center space-x-2 text-gray-600">
										<MapPin className="w-4 h-4" />
										<span>{cat.location}</span>
									</div>
								</div>
							</div>

							{/* Quick Info Grid */}
							<div className="grid grid-cols-2 gap-4 mb-6">
								<div className="text-center content-center p-3 bg-gray-50 rounded-xl">
									<div className="text-lg font-semibold text-gray-900">
										{cat.age}
									</div>
									<div className="text-sm text-gray-600">
										{t("age")}
									</div>
								</div>
								<div className="text-center content-center p-3 bg-gray-50 rounded-xl">
									<div className="text-lg font-semibold text-gray-900 capitalize">
										{cat.gender}
									</div>
									<div className="text-sm text-gray-600">
										{t("gender")}
									</div>
								</div>
								<div className="text-center content-center p-3 bg-gray-50 rounded-xl">
									<div className="text-lg font-semibold text-gray-900">
										{cat.breed}
									</div>
									<div className="text-sm text-gray-600">
										{t("breed")}
									</div>
								</div>
								<div className="text-center content-center p-3 bg-gray-50 rounded-xl">
									<div className="text-lg font-semibold text-gray-900">
										{t("medium")}
									</div>
									<div className="text-sm text-gray-600">
										{t("size")}
									</div>
								</div>
							</div>

							{/* Primary CTA */}
							<div className="space-y-3">
								{session && (
									<ChatButton
										className="w-full"
										catId={cat.id.toString()}
										userId={cat.userId?.toString() || ""}
									/>
								)}
								<div className="flex space-x-2">
									<Button
										variant="outline"
										className={cn(
											"flex-1 px-6 py-3 border font-medium rounded-xl transition-colors min-h-[44px] cursor-pointer",
											isCatFavorite
												? "border-red-500 text-red-500 bg-red-50 hover:bg-red-100"
												: "border-gray-300 hover:border-gray-400 text-gray-700"
										)}
										onClick={() =>
											handleToggleFavorite(cat.id)
										}
										disabled={isFavoritePending}
									>
										{isFavoritePending ? (
											<Loader2 className="w-5 h-5 mr-2 animate-spin" />
										) : (
											<Heart
												className={cn(
													"w-5 h-5 mr-2 transition-colors",
													isCatFavorite
														? "fill-red-500"
														: ""
												)}
											/>
										)}
										{isCatFavorite
											? t("favorite")
											: t("favorite")}
									</Button>
									<Button
										variant="outline"
										className="flex-1 px-6 py-3 border border-gray-300 hover:border-gray-400 text-gray-700 font-medium rounded-xl transition-colors min-h-[44px]"
									>
										<Share2 className="w-5 h-5 mr-2" />
										{t("share")}
									</Button>
								</div>
							</div>

							{/* Admin Controls */}
							{canEdit && (
								<div className="mt-4 pt-4 border-t border-gray-200">
									<div className="flex space-x-2">
										<Button
											asChild
											variant="outline"
											className="flex-1 min-h-[44px]"
										>
											<Link
												href={`/${lang}/cats/${slug}/edit`}
											>
												<Edit className="h-4 w-4 mr-2" />
												{common("edit")}
											</Link>
										</Button>
										<div className="flex-1">
											<CatStatusMenu
												catId={cat.id.toString()}
												currentStatus={
													cat.status ||
													(cat.adopted
														? "adopted"
														: "available")
												}
											/>
										</div>
									</div>
								</div>
							)}

							{/* Caretaker Information */}
							{cat.user && (
								<div className="mt-6 border-t border-gray-200">
									<div className="rounded-2xl pt-4">
										<h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
											{t("currentCaretaker")}
										</h3>
										<div className="flex items-center space-x-4">
											<Avatar className="h-12 w-12 flex-shrink-0">
												<AvatarImage
													src={
														cat.user.image ||
														undefined
													}
													alt={
														cat.user.name || "User"
													}
												/>
												<AvatarFallback className="bg-teal-100 text-teal-700 text-lg font-semibold">
													{cat.user.name
														?.charAt(0)
														.toUpperCase() || "?"}
												</AvatarFallback>
											</Avatar>
											<div className="flex-1 min-w-0">
												<p className="font-semibold text-gray-900 truncate">
													{cat.user.name}
												</p>
												<p className="text-sm text-gray-600 truncate">
													{cat.location ||
														t("details.noLocation")}
												</p>
											</div>
											<Button
												asChild
												variant="outline"
												className="border-teal-600 text-sm text-teal-600 hover:bg-teal-50 rounded-xl min-h-[44px] px-4 flex-shrink-0"
											>
												<Link
													href={`/${lang}/users/${cat.user.slug}`}
												>
													{t("viewProfile")}
												</Link>
											</Button>
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
