import {
	cats,
	users,
	catBreeds,
	wilayas,
	communes,
	catImages,
} from "@/lib/db/schema";
import {
	eq,
	and,
	count,
	desc,
	asc,
	gte,
	lte,
	ilike,
	or,
	inArray,
} from "drizzle-orm";
import { CatSummary } from "@/lib/types/cat";

// Helper function to format cat data consistently
export function formatCatSummary(cat: any): CatSummary {
	return {
		...cat,
		id: cat.id.toString(),
		breed: cat.breed?.name || "Mixed",
		location: cat.wilaya?.name
			? cat.commune
				? `${cat.commune.name}, ${cat.wilaya.name}`
				: cat.wilaya.name
			: "Unknown",
		imageUrl:
			cat.images?.find((img: any) => img.isPrimary)?.url ||
			cat.images?.[0]?.url ||
			"/cat.jpeg?height=300&width=400",
	};
}

// Helper function to get user with role
export async function getUserWithRole(ctx: any, userId: string) {
	const user = await ctx.db.query.users.findFirst({
		where: eq(users.id, parseInt(userId)),
	});
	return user;
}

// Helper function to fetch missing related data for display formatting
export async function fetchMissingRelatedData(
	db: any,
	cats: any[],
	joinRequirements: any
) {
	const startTime = performance.now();

	// Fetch breeds if not included in main query
	let breedsMap = new Map();
	if (!joinRequirements.needsBreed && cats.some((cat) => cat.breedId)) {
		const breedIds = [
			...new Set(cats.map((cat) => cat.breedId).filter(Boolean)),
		];
		if (breedIds.length > 0) {
			const breeds = await db.query.catBreeds.findMany({
				where: inArray(catBreeds.id, breedIds),
			});
			breeds.forEach((breed: any) => breedsMap.set(breed.id, breed));
		}
	}

	// Fetch wilayas if not included in main query
	let wilayasMap = new Map();
	if (!joinRequirements.needsWilaya && cats.some((cat) => cat.wilayaId)) {
		const wilayaIds = [
			...new Set(cats.map((cat) => cat.wilayaId).filter(Boolean)),
		];
		if (wilayaIds.length > 0) {
			const wilayaResults = await db.query.wilayas.findMany({
				where: inArray(wilayas.id, wilayaIds),
			});
			wilayaResults.forEach((wilaya: any) =>
				wilayasMap.set(wilaya.id, wilaya)
			);
		}
	}

	// Fetch communes if not included in main query
	let communesMap = new Map();
	if (!joinRequirements.needsCommune && cats.some((cat) => cat.communeId)) {
		const communeIds = [
			...new Set(cats.map((cat) => cat.communeId).filter(Boolean)),
		];
		if (communeIds.length > 0) {
			const communeResults = await db.query.communes.findMany({
				where: inArray(communes.id, communeIds),
			});
			communeResults.forEach((commune: any) =>
				communesMap.set(commune.id, commune)
			);
		}
	}

	// Attach the missing related data to cats
	const result = cats.map((cat) => ({
		...cat,
		breed: cat.breed || breedsMap.get(cat.breedId) || null,
		wilaya: cat.wilaya || wilayasMap.get(cat.wilayaId) || null,
		commune: cat.commune || communesMap.get(cat.communeId) || null,
	}));

	const duration = performance.now() - startTime;
	logSlowQuery("fetchMissingRelatedData", duration);

	return result;
}

// Helper function to transform query results with images into expected format
export function transformCatResultsWithImages(results: any[], limit?: number) {
	// Group results by cat ID to handle potential duplicates from JOINs
	const catsMap = new Map();

	results.forEach((result) => {
		const catId = result.id;
		const imageData = result.images;

		// Create the cat object without the images property
		const { images, ...catData } = result;

		if (catsMap.has(catId)) {
			// Cat already exists, just add the image if it's not already there
			const existingCat = catsMap.get(catId);
			if (
				imageData &&
				!existingCat.images.some((img: any) => img.id === imageData.id)
			) {
				existingCat.images.push(imageData);
			}
		} else {
			// New cat, add it to the map
			catsMap.set(catId, {
				...catData,
				images: imageData ? [imageData] : [],
			});
		}
	});

	// Convert map back to array and apply limit if specified
	const uniqueCats = Array.from(catsMap.values());
	return limit ? uniqueCats.slice(0, limit) : uniqueCats;
}

// Helper function to analyze JOIN requirements based on input
export function analyzeJoinRequirements(
	input: any,
	hasSearch: boolean = false
) {
	const requirements = {
		// JOINs needed for filtering or search
		needsBreed: hasSearch || (input?.breedId && input.breedId !== "all"),
		needsWilaya: hasSearch || (input?.wilayaId && input.wilayaId !== "all"),
		needsCommune:
			hasSearch || (input?.communeId && input.communeId !== "all"),
		needsUser: hasSearch, // Only for search queries
		// Include images in main query to eliminate N+1 problem
		needsImages: true,
		// Track if JOINs are needed for filtering vs display
		breedForFilter: input?.breedId && input.breedId !== "all",
		wilayaForFilter: input?.wilayaId && input.wilayaId !== "all",
		communeForFilter: input?.communeId && input.communeId !== "all",
	};

	return requirements;
}

// Helper function to build filter conditions with JOIN requirements
export function buildCatFilters(input: any) {
	const conditions = [];

	// Always exclude drafts
	conditions.push(eq(cats.isDraft, false));

	if (input?.gender && input.gender !== "all") {
		conditions.push(eq(cats.gender, input.gender));
	}

	if (input?.breedId && input.breedId !== "all") {
		conditions.push(eq(cats.breedId, parseInt(input.breedId)));
	}

	if (input?.wilayaId && input.wilayaId !== "all") {
		conditions.push(eq(cats.wilayaId, parseInt(input.wilayaId)));
	}

	if (input?.communeId && input.communeId !== "all") {
		conditions.push(eq(cats.communeId, parseInt(input.communeId)));
	}

	if (input?.specialNeeds) {
		conditions.push(eq(cats.specialNeeds, true));
	}

	if (input?.vaccinated) {
		conditions.push(eq(cats.vaccinated, true));
	}

	if (input?.neutered) {
		conditions.push(eq(cats.neutered, true));
	}

	if (input?.notAdopted) {
		conditions.push(eq(cats.adopted, false));
	}

	if (input?.ageMin) {
		conditions.push(gte(cats.age, input.ageMin));
	}

	if (input?.ageMax) {
		conditions.push(lte(cats.age, input.ageMax));
	}

	return conditions;
}

// Helper function to build search conditions
export function buildSearchConditions(searchTerm: string) {
	const searchPattern = `%${searchTerm.trim()}%`;
	return or(
		// Cat fields - leveraging our new indexes
		ilike(cats.name, searchPattern),
		ilike(cats.description, searchPattern),
		ilike(cats.story, searchPattern),
		ilike(cats.specialNeedsDescription, searchPattern),
		// Breed name - leveraging breed name index
		ilike(catBreeds.name, searchPattern),
		// Location fields - leveraging location indexes
		ilike(wilayas.name, searchPattern),
		ilike(wilayas.nameAr, searchPattern),
		ilike(wilayas.nameFr, searchPattern),
		ilike(communes.name, searchPattern),
		ilike(communes.nameAr, searchPattern),
		ilike(communes.nameFr, searchPattern),
		// User name - leveraging user name index
		ilike(users.name, searchPattern)
	);
}

// Helper function to get sort order
export function getSortOrder(sort?: string) {
	switch (sort) {
		case "oldest":
			return asc(cats.createdAt);
		case "name_asc":
			return asc(cats.name);
		case "name_desc":
			return desc(cats.name);
		default:
			return desc(cats.createdAt);
	}
}

// Performance monitoring helper
export function logSlowQuery(
	queryName: string,
	duration: number,
	threshold = 1000
) {
	if (duration > threshold) {
		console.warn(
			`🐌 Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`
		);
	} else if (duration < threshold) {
		console.info(
			`⚡ Query performance: ${queryName} took ${duration.toFixed(2)}ms`
		);
	}
}

// Optimized query builder with conditional JOINs
export async function getOptimizedCatQuery(
	db: any,
	baseConditions: any[],
	sortOrder: any,
	limit: number,
	offset: number,
	joinRequirements: any,
	searchConditions?: any
) {
	const startTime = performance.now();
	const queryName = searchConditions ? "optimizedSearch" : "optimizedListing";

	// When including images, we might get duplicates from JOINs, so we need to fetch more
	// and then deduplicate. Apply a buffer to ensure we get enough unique results.
	const queryLimit = joinRequirements.needsImages ? limit * 2 : limit;

	// Build the select object with conditional related data
	const selectObject: any = {
		// Cat data (always selected)
		id: cats.id,
		slug: cats.slug,
		name: cats.name,
		gender: cats.gender,
		age: cats.age,
		breedId: cats.breedId,
		description: cats.description,
		story: cats.story,
		wilayaId: cats.wilayaId,
		communeId: cats.communeId,
		vaccinated: cats.vaccinated,
		neutered: cats.neutered,
		specialNeeds: cats.specialNeeds,
		specialNeedsDescription: cats.specialNeedsDescription,
		adopted: cats.adopted,
		status: cats.status,
		isDraft: cats.isDraft,
		featured: cats.featured,
		userId: cats.userId,
		createdAt: cats.createdAt,
		updatedAt: cats.updatedAt,
	};

	// Add conditional related data only when JOINs are included
	if (joinRequirements.needsBreed) {
		selectObject.breed = catBreeds;
	}
	if (joinRequirements.needsWilaya) {
		selectObject.wilaya = wilayas;
	}
	if (joinRequirements.needsCommune) {
		selectObject.commune = communes;
	}
	if (joinRequirements.needsUser) {
		selectObject.user = users;
	}
	if (joinRequirements.needsImages) {
		selectObject.images = catImages;
	}

	// Build the query with conditional JOINs
	let query = db.select(selectObject).from(cats);

	// Add conditional JOINs
	if (joinRequirements.needsBreed) {
		query = query.leftJoin(catBreeds, eq(cats.breedId, catBreeds.id));
	}
	if (joinRequirements.needsWilaya) {
		query = query.leftJoin(wilayas, eq(cats.wilayaId, wilayas.id));
	}
	if (joinRequirements.needsCommune) {
		query = query.leftJoin(communes, eq(cats.communeId, communes.id));
	}
	if (joinRequirements.needsUser) {
		query = query.leftJoin(users, eq(cats.userId, users.id));
	}
	if (joinRequirements.needsImages) {
		// LEFT JOIN to get only primary images to avoid duplicating cat records
		query = query.leftJoin(
			catImages,
			and(eq(cats.id, catImages.catId), eq(catImages.isPrimary, true))
		);
	}

	// Add WHERE conditions
	const whereConditions = searchConditions
		? and(...baseConditions, searchConditions)
		: baseConditions.length > 0
			? and(...baseConditions)
			: undefined;

	if (whereConditions) {
		query = query.where(whereConditions);
	}

	// Add ordering, limit, and offset
	const results = await query
		.orderBy(sortOrder)
		.limit(queryLimit)
		.offset(offset);

	const duration = performance.now() - startTime;
	logSlowQuery(queryName, duration);

	return results;
}

// Optimized count query with conditional JOINs
export async function getOptimizedCountQuery(
	db: any,
	baseConditions: any[],
	joinRequirements: any,
	searchConditions?: any
) {
	const startTime = performance.now();
	const queryName = searchConditions
		? "optimizedSearchCount"
		: "optimizedCount";

	// Build count query with conditional JOINs
	let query = db.select({ value: count() }).from(cats);

	// Add conditional JOINs only if needed for search or filtering
	if (searchConditions) {
		// Search requires all JOINs
		query = query
			.leftJoin(catBreeds, eq(cats.breedId, catBreeds.id))
			.leftJoin(wilayas, eq(cats.wilayaId, wilayas.id))
			.leftJoin(communes, eq(cats.communeId, communes.id))
			.leftJoin(users, eq(cats.userId, users.id));
	} else {
		// Non-search: only add JOINs if needed for filtering
		if (joinRequirements.breedForFilter) {
			query = query.leftJoin(catBreeds, eq(cats.breedId, catBreeds.id));
		}
		if (joinRequirements.wilayaForFilter) {
			query = query.leftJoin(wilayas, eq(cats.wilayaId, wilayas.id));
		}
		if (joinRequirements.communeForFilter) {
			query = query.leftJoin(communes, eq(cats.communeId, communes.id));
		}
		// User JOIN not needed for non-search filtering
	}

	// Add WHERE conditions
	const whereConditions = searchConditions
		? and(...baseConditions, searchConditions)
		: baseConditions.length > 0
			? and(...baseConditions)
			: undefined;

	if (whereConditions) {
		query = query.where(whereConditions);
	}

	const [{ value: totalCount }] = await query;

	const duration = performance.now() - startTime;
	logSlowQuery(queryName, duration);

	return totalCount;
}
